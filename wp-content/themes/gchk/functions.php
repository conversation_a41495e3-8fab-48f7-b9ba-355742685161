<?php

/**
 * TwentyTen functions and definitions
 *
 * Sets up the theme and provides some helper functions. Some helper functions
 * are used in the theme as custom template tags. Others are attached to action and
 * filter hooks in WordPress to change core functionality.
 *
 * The first function, twentyten_setup(), sets up the theme by registering support
 * for various features in WordPress, such as post thumbnails, navigation menus, and the like.
 *
 * When using a child theme you can override certain functions (those wrapped
 * in a function_exists() call) by defining them first in your child theme's
 * functions.php file. The child theme's functions.php file is included before
 * the parent theme's file, so the child theme functions would be used.
 *
 * @link https://developer.wordpress.org/themes/basics/theme-functions/
 * @link https://developer.wordpress.org/themes/advanced-topics/child-themes/
 *
 * Functions that are not pluggable (not wrapped in function_exists()) are instead attached
 * to a filter or action hook. The hook can be removed by using remove_action() or
 * remove_filter() and you can attach your own function to the hook.
 *
 * We can remove the parent theme's hook only after it is attached, which means we need to
 * wait until setting up the child theme:
 *
 * <code>
 * add_action( 'after_setup_theme', 'my_child_theme_setup' );
 * function my_child_theme_setup() {
 *     // We are providing our own filter for excerpt_length (or using the unfiltered value).
 *     remove_filter( 'excerpt_length', 'twentyten_excerpt_length' );
 *     ...
 * }
 * </code>
 *
 * For more information on hooks, actions, and filters, see https://developer.wordpress.org/plugins/.
 *
 * @package WordPress
 * @subpackage Twenty_Ten
 * @since Twenty Ten 1.0
 */
// Load Composer autoloader
if (file_exists(__DIR__ . '/vendor/autoload.php')) {
	require_once __DIR__ . '/vendor/autoload.php';
}

use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;
/*
 * Set the content width based on the theme's design and stylesheet.
 *
 * Used to set the width of images and content. Should be equal to the width the theme
 * is designed for, generally via the style.css stylesheet.
 */

if (! isset($content_width)) {
	$content_width = 640;
}

/* Tell WordPress to run twentyten_setup() when the 'after_setup_theme' hook is run. */
add_action('after_setup_theme', 'twentyten_setup');

if (! function_exists('twentyten_setup')) :
	/**
	 * Set up theme defaults and registers support for various WordPress features.
	 *
	 * Note that this function is hooked into the after_setup_theme hook, which runs
	 * before the init hook. The init hook is too late for some features, such as indicating
	 * support post thumbnails.
	 *
	 * To override twentyten_setup() in a child theme, add your own twentyten_setup to your child theme's
	 * functions.php file.
	 *
	 * @uses add_theme_support()        To add support for post thumbnails, custom headers and backgrounds, and automatic feed links.
	 * @uses register_nav_menus()       To add support for navigation menus.
	 * @uses add_editor_style()         To style the visual editor.
	 * @uses load_theme_textdomain()    For translation/localization support.
	 * @uses register_default_headers() To register the default custom header images provided with the theme.
	 * @uses set_post_thumbnail_size()  To set a custom post thumbnail size.
	 *
	 * @since Twenty Ten 1.0
	 */
	function twentyten_setup()
	{

		// This theme styles the visual editor with editor-style.css to match the theme style.
		add_editor_style();

		// Load regular editor styles into the new block-based editor.
		add_theme_support('editor-styles');

		// Load default block styles.
		add_theme_support('wp-block-styles');

		// Add support for custom color scheme.
		add_theme_support(
			'editor-color-palette',
			array(
				array(
					'name'  => __('Blue', 'twentyten'),
					'slug'  => 'blue',
					'color' => '#0066cc',
				),
				array(
					'name'  => __('Black', 'twentyten'),
					'slug'  => 'black',
					'color' => '#000',
				),
				array(
					'name'  => __('Medium Gray', 'twentyten'),
					'slug'  => 'medium-gray',
					'color' => '#666',
				),
				array(
					'name'  => __('Light Gray', 'twentyten'),
					'slug'  => 'light-gray',
					'color' => '#f1f1f1',
				),
				array(
					'name'  => __('White', 'twentyten'),
					'slug'  => 'white',
					'color' => '#fff',
				),
			)
		);

		// Post Format support. You can also use the legacy "gallery" or "asides" (note the plural) categories.
		add_theme_support('post-formats', array('aside', 'gallery'));

		// This theme uses post thumbnails.
		add_theme_support('post-thumbnails');

		// Add default posts and comments RSS feed links to head.
		add_theme_support('automatic-feed-links');

		/*
		 * Make theme available for translation.
		 * Translations can be filed in the /languages/ directory.
		 *
		 * Manual loading of text domain is not required after the introduction of
		 * just in time translation loading in WordPress version 4.6.
		 *
		 * @ticket 58318
		 */
		if (version_compare($GLOBALS['wp_version'], '4.6', '<')) {
			load_theme_textdomain('twentyten', get_template_directory() . '/languages');
		}

		// This theme uses wp_nav_menu() in one location.
		register_nav_menus(
			array(
				'primary' => __('Primary Navigation', 'twentyten'),
			)
		);

		// This theme allows users to set a custom background.
		add_theme_support(
			'custom-background',
			array(
				// Let WordPress know what our default background color is.
				'default-color' => 'f1f1f1',
			)
		);

		// The custom header business starts here.

		$custom_header_support = array(
			/*
			 * The default image to use.
			 * The %s is a placeholder for the theme template directory URI.
			 */
			'default-image'       => '%s/images/headers/path.jpg',
			// The height and width of our custom header.
			/**
			 * Filters the Twenty Ten default header image width.
			 *
			 * @since Twenty Ten 1.0
			 *
			 * @param int The default header image width in pixels. Default 940.
			 */
			'width'               => apply_filters('twentyten_header_image_width', 940),
			/**
			 * Filters the Twenty Ten default header image height.
			 *
			 * @since Twenty Ten 1.0
			 *
			 * @param int The default header image height in pixels. Default 198.
			 */
			'height'              => apply_filters('twentyten_header_image_height', 198),
			// Support flexible heights.
			'flex-height'         => true,
			// Don't support text inside the header image.
			'header-text'         => false,
			// Callback for styling the header preview in the admin.
			'admin-head-callback' => 'twentyten_admin_header_style',
		);

		add_theme_support('custom-header', $custom_header_support);

		if (! function_exists('get_custom_header')) {
			// This is all for compatibility with versions of WordPress prior to 3.4.
			define('HEADER_TEXTCOLOR', '');
			define('NO_HEADER_TEXT', true);
			define('HEADER_IMAGE', $custom_header_support['default-image']);
			define('HEADER_IMAGE_WIDTH', $custom_header_support['width']);
			define('HEADER_IMAGE_HEIGHT', $custom_header_support['height']);
			add_custom_image_header('', $custom_header_support['admin-head-callback']);
			add_custom_background();
		}

		/*
		 * We'll be using post thumbnails for custom header images on posts and pages.
		 * We want them to be 940 pixels wide by 198 pixels tall.
		 * Larger images will be auto-cropped to fit, smaller ones will be ignored. See header.php.
		 */
		set_post_thumbnail_size($custom_header_support['width'], $custom_header_support['height'], true);

		// ...and thus ends the custom header business.

		// Default custom headers packaged with the theme. %s is a placeholder for the theme template directory URI.
		register_default_headers(
			array(
				'berries'       => array(
					'url'           => '%s/images/headers/berries.jpg',
					'thumbnail_url' => '%s/images/headers/berries-thumbnail.jpg',
					/* translators: Header image description. */
					'description'   => __('Berries', 'twentyten'),
				),
				'cherryblossom' => array(
					'url'           => '%s/images/headers/cherryblossoms.jpg',
					'thumbnail_url' => '%s/images/headers/cherryblossoms-thumbnail.jpg',
					/* translators: Header image description. */
					'description'   => __('Cherry Blossoms', 'twentyten'),
				),
				'concave'       => array(
					'url'           => '%s/images/headers/concave.jpg',
					'thumbnail_url' => '%s/images/headers/concave-thumbnail.jpg',
					/* translators: Header image description. */
					'description'   => __('Concave', 'twentyten'),
				),
				'fern'          => array(
					'url'           => '%s/images/headers/fern.jpg',
					'thumbnail_url' => '%s/images/headers/fern-thumbnail.jpg',
					/* translators: Header image description. */
					'description'   => __('Fern', 'twentyten'),
				),
				'forestfloor'   => array(
					'url'           => '%s/images/headers/forestfloor.jpg',
					'thumbnail_url' => '%s/images/headers/forestfloor-thumbnail.jpg',
					/* translators: Header image description. */
					'description'   => __('Forest Floor', 'twentyten'),
				),
				'inkwell'       => array(
					'url'           => '%s/images/headers/inkwell.jpg',
					'thumbnail_url' => '%s/images/headers/inkwell-thumbnail.jpg',
					/* translators: Header image description. */
					'description'   => __('Inkwell', 'twentyten'),
				),
				'path'          => array(
					'url'           => '%s/images/headers/path.jpg',
					'thumbnail_url' => '%s/images/headers/path-thumbnail.jpg',
					/* translators: Header image description. */
					'description'   => __('Path', 'twentyten'),
				),
				'sunset'        => array(
					'url'           => '%s/images/headers/sunset.jpg',
					'thumbnail_url' => '%s/images/headers/sunset-thumbnail.jpg',
					/* translators: Header image description. */
					'description'   => __('Sunset', 'twentyten'),
				),
			)
		);
	}
endif;

if (! function_exists('twentyten_admin_header_style')) :
	/**
	 * Style the header image displayed on the Appearance > Header admin panel.
	 *
	 * Referenced via add_custom_image_header() in twentyten_setup().
	 *
	 * @since Twenty Ten 1.0
	 */
	function twentyten_admin_header_style()
	{
?>
		<style type="text/css" id="twentyten-admin-header-css">
			/* Shows the same border as on front end */
			#headimg {
				border-bottom: 1px solid #000;
				border-top: 4px solid #000;
			}

			/* If header-text was supported, you would style the text with these selectors:
	#headimg #name { }
	#headimg #desc { }
	*/
		</style>
	<?php
	}
endif;


if (! function_exists('twentyten_header_image')) :
	/**
	 * Custom header image markup displayed.
	 *
	 * @since Twenty Ten 4.0
	 */
	function twentyten_header_image()
	{
		$attrs = array(
			'alt' => get_bloginfo('name', 'display'),
		);

		// Compatibility with versions of WordPress prior to 3.4.
		if (function_exists('get_custom_header')) {
			$custom_header   = get_custom_header();
			$attrs['width']  = $custom_header->width;
			$attrs['height'] = $custom_header->height;
		} else {
			$attrs['width']  = HEADER_IMAGE_WIDTH;
			$attrs['height'] = HEADER_IMAGE_HEIGHT;
		}

		if (function_exists('the_header_image_tag')) {
			the_header_image_tag($attrs);
			return;
		}

	?>
		<img src="<?php header_image(); ?>" width="<?php echo esc_attr($attrs['width']); ?>" height="<?php echo esc_attr($attrs['height']); ?>" alt="<?php echo esc_attr($attrs['alt']); ?>" />
		<?php
	}
endif; // twentyten_header_image()

/**
 * Show a home link for our wp_nav_menu() fallback, wp_page_menu().
 *
 * To override this in a child theme, remove the filter and optionally add
 * your own function tied to the wp_page_menu_args filter hook.
 *
 * @since Twenty Ten 1.0
 *
 * @param array $args An optional array of arguments. @see wp_page_menu()
 */
function twentyten_page_menu_args($args)
{
	if (! isset($args['show_home'])) {
		$args['show_home'] = true;
	}
	return $args;
}
add_filter('wp_page_menu_args', 'twentyten_page_menu_args');

/**
 * Set the post excerpt length to 40 characters.
 *
 * To override this length in a child theme, remove the filter and add your own
 * function tied to the excerpt_length filter hook.
 *
 * @since Twenty Ten 1.0
 *
 * @param int $length The number of excerpt characters.
 * @return int The filtered number of excerpt characters.
 */
function twentyten_excerpt_length($length)
{
	return 40;
}
add_filter('excerpt_length', 'twentyten_excerpt_length');

if (! function_exists('twentyten_continue_reading_link')) :
	/**
	 * Return a "Continue Reading" link for excerpts.
	 *
	 * @since Twenty Ten 1.0
	 *
	 * @return string "Continue Reading" link.
	 */
	function twentyten_continue_reading_link()
	{
		return ' <a href="' . esc_url(get_permalink()) . '">' . __('Continue reading <span class="meta-nav">&rarr;</span>', 'twentyten') . '</a>';
	}
endif;

/**
 * Replace "[...]" with an ellipsis and twentyten_continue_reading_link().
 *
 * "[...]" is appended to automatically generated excerpts.
 *
 * To override this in a child theme, remove the filter and add your own
 * function tied to the excerpt_more filter hook.
 *
 * @since Twenty Ten 1.0
 *
 * @param string $more The Read More text.
 * @return string The filtered Read More text.
 */
function twentyten_auto_excerpt_more($more)
{
	if (! is_admin()) {
		return ' &hellip;' . twentyten_continue_reading_link();
	}
	return $more;
}
add_filter('excerpt_more', 'twentyten_auto_excerpt_more');

/**
 * Add a pretty "Continue Reading" link to custom post excerpts.
 *
 * To override this link in a child theme, remove the filter and add your own
 * function tied to the get_the_excerpt filter hook.
 *
 * @since Twenty Ten 1.0
 *
 * @param string $output The "Continue Reading" link.
 * @return string Excerpt with a pretty "Continue Reading" link.
 */
function twentyten_custom_excerpt_more($output)
{
	if (has_excerpt() && ! is_attachment() && ! is_admin()) {
		$output .= twentyten_continue_reading_link();
	}
	return $output;
}
add_filter('get_the_excerpt', 'twentyten_custom_excerpt_more');

/**
 * Remove inline styles printed when the gallery shortcode is used.
 *
 * Galleries are styled by the theme in Twenty Ten's style.css. This is just
 * a simple filter call that tells WordPress to not use the default styles.
 *
 * @since Twenty Ten 1.2
 */
add_filter('use_default_gallery_style', '__return_false');

/**
 * Deprecated way to remove inline styles printed when the gallery shortcode is used.
 *
 * This function is no longer needed or used. Use the use_default_gallery_style
 * filter instead, as seen above.
 *
 * @since Twenty Ten 1.0
 * @deprecated Deprecated in Twenty Ten 1.2 for WordPress 3.1
 *
 * @param string $css Default CSS styles and opening HTML div container
 *                    for the gallery shortcode output.
 * @return string The gallery style filter, with the styles themselves removed.
 */
function twentyten_remove_gallery_css($css)
{
	return preg_replace("#<style type='text/css'>(.*?)</style>#s", '', $css);
}
// Backward compatibility with WordPress 3.0.
if (version_compare($GLOBALS['wp_version'], '3.1', '<')) {
	add_filter('gallery_style', 'twentyten_remove_gallery_css');
}

if (! function_exists('twentyten_comment')) :
	/**
	 * Template for comments and pingbacks.
	 *
	 * To override this walker in a child theme without modifying the comments template
	 * simply create your own twentyten_comment(), and that function will be used instead.
	 *
	 * Used as a callback by wp_list_comments() for displaying the comments.
	 *
	 * @since Twenty Ten 1.0
	 *
	 * @param WP_Comment $comment The comment object.
	 * @param array      $args    An array of arguments. @see get_comment_reply_link()
	 * @param int        $depth   The depth of the comment.
	 */
	function twentyten_comment($comment, $args, $depth)
	{
		$GLOBALS['comment'] = $comment;
		switch ($comment->comment_type):
			case '':
			case 'comment':
		?>
				<li <?php comment_class(); ?> id="li-comment-<?php comment_ID(); ?>">
					<div id="comment-<?php comment_ID(); ?>">
						<div class="comment-author vcard">
							<?php echo get_avatar($comment, 40); ?>
							<?php
							/* translators: %s: Author display name. */
							printf(__('%s <span class="says">says:</span>', 'twentyten'), sprintf('<cite class="fn">%s</cite>', get_comment_author_link()));
							?>
						</div><!-- .comment-author .vcard -->

						<?php
						$commenter = wp_get_current_commenter();
						if ($commenter['comment_author_email']) {
							$moderation_note = __('Your comment is awaiting moderation.', 'twentyten');
						} else {
							$moderation_note = __('Your comment is awaiting moderation. This is a preview; your comment will be visible after it has been approved.', 'twentyten');
						}
						?>

						<?php if ('0' === $comment->comment_approved) : ?>
							<em class="comment-awaiting-moderation"><?php echo $moderation_note; ?></em>
							<br />
						<?php endif; ?>

						<div class="comment-meta commentmetadata"><a href="<?php echo esc_url(get_comment_link($comment->comment_ID)); ?>">
								<?php
								/* translators: 1: Date, 2: Time. */
								printf(__('%1$s at %2$s', 'twentyten'), get_comment_date(), get_comment_time());
								?>
							</a>
							<?php
							edit_comment_link(__('(Edit)', 'twentyten'), ' ');
							?>
						</div><!-- .comment-meta .commentmetadata -->

						<div class="comment-body"><?php comment_text(); ?></div>

						<div class="reply">
							<?php
							comment_reply_link(
								array_merge(
									$args,
									array(
										'depth'     => $depth,
										'max_depth' => $args['max_depth'],
									)
								)
							);
							?>
						</div><!-- .reply -->
					</div><!-- #comment-##  -->

				<?php
				break;
			case 'pingback':
			case 'trackback':
				?>
				<li class="post pingback">
					<p><?php _e('Pingback:', 'twentyten'); ?> <?php comment_author_link(); ?><?php edit_comment_link(__('(Edit)', 'twentyten'), ' '); ?></p>
		<?php
				break;
		endswitch;
	}
endif;

/**
 * Register widgetized areas, including two sidebars and four widget-ready columns in the footer.
 *
 * To override twentyten_widgets_init() in a child theme, remove the action hook and add your own
 * function tied to the init hook.
 *
 * @since Twenty Ten 1.0
 *
 * @uses register_sidebar()
 */
function twentyten_widgets_init()
{
	// Area 1, located at the top of the sidebar.
	register_sidebar(
		array(
			'name'          => __('Primary Widget Area', 'twentyten'),
			'id'            => 'primary-widget-area',
			'description'   => __('Add widgets here to appear in your sidebar.', 'twentyten'),
			'before_widget' => '<li id="%1$s" class="widget-container %2$s">',
			'after_widget'  => '</li>',
			'before_title'  => '<h3 class="widget-title">',
			'after_title'   => '</h3>',
		)
	);

	// Area 2, located below the Primary Widget Area in the sidebar. Empty by default.
	register_sidebar(
		array(
			'name'          => __('Secondary Widget Area', 'twentyten'),
			'id'            => 'secondary-widget-area',
			'description'   => __('An optional secondary widget area, displays below the primary widget area in your sidebar.', 'twentyten'),
			'before_widget' => '<li id="%1$s" class="widget-container %2$s">',
			'after_widget'  => '</li>',
			'before_title'  => '<h3 class="widget-title">',
			'after_title'   => '</h3>',
		)
	);

	// Area 3, located in the footer. Empty by default.
	register_sidebar(
		array(
			'name'          => __('First Footer Widget Area', 'twentyten'),
			'id'            => 'first-footer-widget-area',
			'description'   => __('An optional widget area for your site footer.', 'twentyten'),
			'before_widget' => '<li id="%1$s" class="widget-container %2$s">',
			'after_widget'  => '</li>',
			'before_title'  => '<h3 class="widget-title">',
			'after_title'   => '</h3>',
		)
	);

	// Area 4, located in the footer. Empty by default.
	register_sidebar(
		array(
			'name'          => __('Second Footer Widget Area', 'twentyten'),
			'id'            => 'second-footer-widget-area',
			'description'   => __('An optional widget area for your site footer.', 'twentyten'),
			'before_widget' => '<li id="%1$s" class="widget-container %2$s">',
			'after_widget'  => '</li>',
			'before_title'  => '<h3 class="widget-title">',
			'after_title'   => '</h3>',
		)
	);

	// Area 5, located in the footer. Empty by default.
	register_sidebar(
		array(
			'name'          => __('Third Footer Widget Area', 'twentyten'),
			'id'            => 'third-footer-widget-area',
			'description'   => __('An optional widget area for your site footer.', 'twentyten'),
			'before_widget' => '<li id="%1$s" class="widget-container %2$s">',
			'after_widget'  => '</li>',
			'before_title'  => '<h3 class="widget-title">',
			'after_title'   => '</h3>',
		)
	);

	// Area 6, located in the footer. Empty by default.
	register_sidebar(
		array(
			'name'          => __('Fourth Footer Widget Area', 'twentyten'),
			'id'            => 'fourth-footer-widget-area',
			'description'   => __('An optional widget area for your site footer.', 'twentyten'),
			'before_widget' => '<li id="%1$s" class="widget-container %2$s">',
			'after_widget'  => '</li>',
			'before_title'  => '<h3 class="widget-title">',
			'after_title'   => '</h3>',
		)
	);
}
/** Register sidebars by running twentyten_widgets_init() on the widgets_init hook. */
add_action('widgets_init', 'twentyten_widgets_init');

/**
 * Remove the default styles that are packaged with the Recent Comments widget.
 *
 * To override this in a child theme, remove the filter and optionally add your own
 * function tied to the widgets_init action hook.
 *
 * This function uses a filter (show_recent_comments_widget_style) new in WordPress 3.1
 * to remove the default style. Using Twenty Ten 1.2 in WordPress 3.0 will show the styles,
 * but they won't have any effect on the widget in default Twenty Ten styling.
 *
 * @since Twenty Ten 1.0
 */
function twentyten_remove_recent_comments_style()
{
	add_filter('show_recent_comments_widget_style', '__return_false');
}
add_action('widgets_init', 'twentyten_remove_recent_comments_style');

if (! function_exists('twentyten_posted_on')) :
	/**
	 * Print HTML with meta information for the current post-date/time and author.
	 *
	 * @since Twenty Ten 1.0
	 */
	function twentyten_posted_on()
	{
		printf(
			/* translators: 1: CSS classes, 2: Date, 3: Author display name. */
			__('<span class="%1$s">Posted on</span> %2$s <span class="meta-sep">by</span> %3$s', 'twentyten'),
			'meta-prep meta-prep-author',
			sprintf(
				'<a href="%1$s" title="%2$s" rel="bookmark"><span class="entry-date">%3$s</span></a>',
				esc_url(get_permalink()),
				esc_attr(get_the_time()),
				get_the_date()
			),
			sprintf(
				'<span class="author vcard"><a class="url fn n" href="%1$s" title="%2$s">%3$s</a></span>',
				esc_url(get_author_posts_url(get_the_author_meta('ID'))),
				/* translators: %s: Author display name. */
				esc_attr(sprintf(__('View all posts by %s', 'twentyten'), get_the_author())),
				get_the_author()
			)
		);
	}
endif;

if (! function_exists('twentyten_posted_in')) :
	/**
	 * Print HTML with meta information for the current post (category, tags and permalink).
	 *
	 * @since Twenty Ten 1.0
	 */
	function twentyten_posted_in()
	{
		// Retrieves tag list of current post, separated by commas.
		$tags_list = get_the_tag_list('', ', ');

		if ($tags_list && ! is_wp_error($tags_list)) {
			/* translators: 1: Category name, 2: Tag name, 3: Post permalink, 4: Post title. */
			$posted_in = __('This entry was posted in %1$s and tagged %2$s. Bookmark the <a href="%3$s" title="Permalink to %4$s" rel="bookmark">permalink</a>.', 'twentyten');
		} elseif (is_object_in_taxonomy(get_post_type(), 'category')) {
			/* translators: 1: Category name, 3: Post permalink, 4: Post title. */
			$posted_in = __('This entry was posted in %1$s. Bookmark the <a href="%3$s" title="Permalink to %4$s" rel="bookmark">permalink</a>.', 'twentyten');
		} else {
			/* translators: 3: Post permalink, 4: Post title. */
			$posted_in = __('Bookmark the <a href="%3$s" title="Permalink to %4$s" rel="bookmark">permalink</a>.', 'twentyten');
		}

		// Prints the string, replacing the placeholders.
		printf(
			$posted_in,
			get_the_category_list(', '),
			$tags_list,
			esc_url(get_permalink()),
			the_title_attribute('echo=0')
		);
	}
endif;

/**
 * Retrieve the IDs for images in a gallery.
 *
 * @uses get_post_galleries() First, if available. Falls back to shortcode parsing,
 *                            then as last option uses a get_posts() call.
 *
 * @since Twenty Ten 1.6.
 *
 * @return array List of image IDs from the post gallery.
 */
function twentyten_get_gallery_images()
{
	$images = array();

	if (function_exists('get_post_galleries')) {
		$galleries = get_post_galleries(get_the_ID(), false);
		if (isset($galleries[0]['ids'])) {
			$images = explode(',', $galleries[0]['ids']);
		}
	} else {
		$pattern = get_shortcode_regex();
		preg_match("/$pattern/s", get_the_content(), $match);
		$atts = shortcode_parse_atts($match[3]);
		if (isset($atts['ids'])) {
			$images = explode(',', $atts['ids']);
		}
	}

	if (! $images) {
		$images = get_posts(
			array(
				'fields'         => 'ids',
				'numberposts'    => 999,
				'order'          => 'ASC',
				'orderby'        => 'menu_order',
				'post_mime_type' => 'image',
				'post_parent'    => get_the_ID(),
				'post_type'      => 'attachment',
			)
		);
	}

	return $images;
}

/**
 * Modifies tag cloud widget arguments to display all tags in the same font size
 * and use list format for better accessibility.
 *
 * @since Twenty Ten 2.4
 *
 * @param array $args Arguments for tag cloud widget.
 * @return array The filtered arguments for tag cloud widget.
 */
function twentyten_widget_tag_cloud_args($args)
{
	$args['largest']  = 22;
	$args['smallest'] = 8;
	$args['unit']     = 'pt';
	$args['format']   = 'list';

	return $args;
}
add_filter('widget_tag_cloud_args', 'twentyten_widget_tag_cloud_args');

/**
 * Enqueue scripts and styles for front end.
 *
 * @since Twenty Ten 2.6
 */
function twentyten_scripts_styles()
{
	// Theme block stylesheet.
	wp_enqueue_style('twentyten-block-style', get_template_directory_uri() . '/blocks.css', array(), '20240703');
}
add_action('wp_enqueue_scripts', 'twentyten_scripts_styles');

/**
 * Enqueue styles for the block-based editor.
 *
 * @since Twenty Ten 2.6
 */
function twentyten_block_editor_styles()
{
	// Block styles.
	wp_enqueue_style('twentyten-block-editor-style', get_template_directory_uri() . '/editor-blocks.css', array(), '20240703');
}
add_action('enqueue_block_editor_assets', 'twentyten_block_editor_styles');

/**
 * Register block patterns and pattern categories.
 *
 * @since Twenty Ten 4.3
 */
function twentyten_register_block_patterns()
{
	require get_template_directory() . '/block-patterns.php';
}

add_action('init', 'twentyten_register_block_patterns');

if (! function_exists('wp_body_open')) :
	/**
	 * Fire the wp_body_open action.
	 *
	 * Added for backward compatibility to support pre-5.2.0 WordPress versions.
	 *
	 * @since Twenty Ten 2.9
	 */
	function wp_body_open()
	{
		/**
		 * Triggered after the opening <body> tag.
		 *
		 * @since Twenty Ten 2.9
		 */
		do_action('wp_body_open');
	}
endif;

add_action('init', 'create_posttype');
function create_posttype()
{
	register_post_type(
		'slider',
		array(
			'labels' => array(
				'name' => __('Slider'),
				'singular_name' => __('Slider')
			),
			'public' => true,
			'has_archive' => true,
			'rewrite' => array('slug' => 'slider'),
			'supports' => apply_filters('popmake_popup_supports', array('title', 'editor', 'thumbnail')),

		)
	);
}

add_action('init', 'create_posttype_about');
function create_posttype_about()
{
	register_post_type(
		'about carousel',
		array(
			'labels' => array(
				'name' => __('About Carousel'),
				'singular_name' => __('About Carousel')
			),
			'public' => true,
			'has_archive' => true,
			'rewrite' => array('slug' => 'carousel'),
			'supports' => apply_filters('popmake_popup_supports', array('title', 'editor', 'thumbnail')),

		)
	);
}

add_action('init', 'create_posttype_invest');
function create_posttype_invest()
{
	register_post_type(
		'why invest',
		array(
			'labels' => array(
				'name' => __('Why Invest'),
				'singular_name' => __('Why Invest')
			),
			'public' => true,
			'has_archive' => true,
			'rewrite' => array('slug' => 'why invest'),
			'supports' => apply_filters('popmake_popup_supports', array('title', 'editor', 'thumbnail')),

		)
	);
}
function custom_filter_wpcf7_is_tel($result, $tel)
{
	$result = preg_match('/^\(?\+?([0-9]{1,4})?\)?[-\. ]?(\d{10})$/', $tel);
	return $result;
}

add_filter('wpcf7_is_tel', 'custom_filter_wpcf7_is_tel', 10, 2);

//**************************Custom Join now form By Kaushal **************************/

// Complete functions.php code for Membership Applications

// Register Custom Post Type for Form Entries
add_action('init', 'create_form_entries_post_type');
function create_form_entries_post_type()
{
	register_post_type(
		'form_entries',
		array(
			'labels' => array(
				'name' => __('Form Entries'),
				'singular_name' => __('Form Entry'),
				'menu_name' => __('Form Entries'),
				'add_new' => __('Add New Entry'),
				'add_new_item' => __('Add New Form Entry'),
				'edit_item' => __('Edit Form Entry'),
				'new_item' => __('New Form Entry'),
				'view_item' => __('View Form Entry'),
				'search_items' => __('Search Form Entries'),
				'not_found' => __('No form entries found'),
				'not_found_in_trash' => __('No form entries found in trash')
			),
			'public' => false,
			'show_ui' => true,
			'show_in_menu' => true,
			'menu_position' => 25,
			'menu_icon' => 'dashicons-forms',
			'supports' => array('title'),
			'capability_type' => 'post',
			'capabilities' => array(
				'create_posts' => 'manage_options', // Only admins can create
			),
			'map_meta_cap' => true,
		)
	);
}

// Register Custom Post Type for Membership Applications (keeping existing)
add_action('init', 'create_membership_post_type');
function create_membership_post_type()
{
	register_post_type(
		'membership_app',
		array(
			'labels' => array(
				'name' => __('Membership Applications'),
				'singular_name' => __('Membership Application'),
				'menu_name' => __('Membership Apps'),
				'add_new' => __('Add New Application'),
				'add_new_item' => __('Add New Membership Application'),
				'edit_item' => __('Edit Membership Application'),
				'new_item' => __('New Membership Application'),
				'view_item' => __('View Membership Application'),
				'search_items' => __('Search Membership Applications'),
				'not_found' => __('No membership applications found'),
				'not_found_in_trash' => __('No membership applications found in trash')
			),
			'public' => false,
			'show_ui' => true,
			'show_in_menu' => true,
			'menu_position' => 26,
			'menu_icon' => 'dashicons-groups',
			'supports' => array('title'),
			'capability_type' => 'post',
			'capabilities' => array(
				'create_posts' => 'manage_options', // Only admins can create
			),
			'map_meta_cap' => true,
		)
	);
}

// Create custom database table for user authentication
register_activation_hook(__FILE__, 'create_user_auth_table');
function create_user_auth_table()
{
	global $wpdb;

	$table_name = $wpdb->prefix . 'user_auth_keys';

	$charset_collate = $wpdb->get_charset_collate();

	$sql = "CREATE TABLE $table_name (
			id mediumint(9) NOT NULL AUTO_INCREMENT,
			user_id bigint(20) NOT NULL,
			login_key varchar(255) NOT NULL,
			created_at datetime DEFAULT CURRENT_TIMESTAMP,
			expires_at datetime NOT NULL,
			last_access datetime DEFAULT NULL,
			is_active tinyint(1) DEFAULT 1,
			PRIMARY KEY (id),
			UNIQUE KEY user_id (user_id),
			KEY login_key (login_key)
		) $charset_collate;";

	require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
	dbDelta($sql);
}

// Initialize the table on theme activation
add_action('after_switch_theme', 'create_user_auth_table');

// Add custom columns to form_entries admin list
add_filter('manage_form_entries_posts_columns', 'form_entries_columns');
function form_entries_columns($columns)
{
	$new_columns = array();
	$new_columns['cb'] = $columns['cb'];
	$new_columns['title'] = __('Customer Name');
	$new_columns['customer_email'] = __('Email');
	$new_columns['customer_phone'] = __('Phone');
	$new_columns['country'] = __('Country');
	$new_columns['occupation'] = __('Occupation');
	$new_columns['resend_login'] = __('Actions');
	$new_columns['date'] = __('Submitted');
	return $new_columns;
}

// Fill custom columns with data for form_entries
add_action('manage_form_entries_posts_custom_column', 'form_entries_column_content', 10, 2);
function form_entries_column_content($column, $post_id)
{
	switch ($column) {
		case 'customer_email':
			echo get_post_meta($post_id, '_email_address', true);
			break;
		case 'customer_phone':
			echo get_post_meta($post_id, '_phone_number', true);
			break;
		case 'country':
			echo get_post_meta($post_id, '_country_residence', true);
			break;
		case 'occupation':
			echo get_post_meta($post_id, '_occupation', true);
			break;
		case 'resend_login':
			$email = get_post_meta($post_id, '_email_address', true);
			if ($email) {
				$nonce = wp_create_nonce('resend_login_' . $post_id);
				$resend_url = admin_url('admin-post.php?action=resend_login_id&post_id=' . $post_id . '&_wpnonce=' . $nonce);
				echo '<a href="' . esc_url($resend_url) . '" class="button button-small" onclick="return confirm(\'Are you sure you want to resend the login credentials to this user?\')">Resend Login ID</a>';
			} else {
				echo '<span style="color: #999;">No email</span>';
			}
			break;
	}
}

// Add custom columns to the admin list (keeping existing for membership_app)
add_filter('manage_membership_app_posts_columns', 'membership_app_columns');
function membership_app_columns($columns)
{
	$new_columns = array();
	$new_columns['cb'] = $columns['cb'];
	$new_columns['title'] = __('Full Name');
	$new_columns['email'] = __('Email');
	$new_columns['phone'] = __('Phone');
	$new_columns['country'] = __('Country');
	$new_columns['occupation'] = __('Occupation');
	$new_columns['status'] = __('Status');
	$new_columns['date'] = __('Submitted');
	return $new_columns;
}

// Fill custom columns with data
add_action('manage_membership_app_posts_custom_column', 'membership_app_column_content', 10, 2);
function membership_app_column_content($column, $post_id)
{
	switch ($column) {
		case 'email':
			echo get_post_meta($post_id, '_email_address', true);
			break;
		case 'phone':
			echo get_post_meta($post_id, '_phone_number', true);
			break;
		case 'country':
			echo get_post_meta($post_id, '_country_residence', true);
			break;
		case 'occupation':
			echo get_post_meta($post_id, '_occupation', true);
			break;
		case 'status':
			$status = get_post_meta($post_id, '_application_status', true);
			$status = $status ? $status : 'pending';
			echo '<span class="status-' . $status . '">' . ucfirst(str_replace('_', ' ', $status)) . '</span>';
			break;
	}
}

// Make columns sortable
add_filter('manage_edit-membership_app_sortable_columns', 'membership_app_sortable_columns');
function membership_app_sortable_columns($columns)
{
	$columns['email'] = 'email';
	$columns['country'] = 'country';
	$columns['status'] = 'status';
	return $columns;
}

// Add custom meta boxes
add_action('add_meta_boxes', 'add_membership_meta_boxes');
function add_membership_meta_boxes()
{
	add_meta_box(
		'membership-personal-info',
		'Personal Information',
		'membership_personal_info_callback',
		'membership_app',
		'normal',
		'high'
	);

	add_meta_box(
		'membership-contact-info',
		'Contact Information',
		'membership_contact_info_callback',
		'membership_app',
		'normal',
		'high'
	);

	add_meta_box(
		'membership-government-id',
		'Government Identification',
		'membership_government_id_callback',
		'membership_app',
		'normal',
		'high'
	);

	add_meta_box(
		'membership-emergency-contact',
		'Emergency Contact',
		'membership_emergency_contact_callback',
		'membership_app',
		'normal',
		'high'
	);

	add_meta_box(
		'membership-professional-info',
		'Professional Information',
		'membership_professional_info_callback',
		'membership_app',
		'normal',
		'high'
	);

	add_meta_box(
		'membership-documents',
		'Documents',
		'membership_documents_callback',
		'membership_app',
		'side',
		'high'
	);

	add_meta_box(
		'membership-status',
		'Application Status',
		'membership_status_callback',
		'membership_app',
		'side',
		'high'
	);
}

// Meta box callbacks
function membership_personal_info_callback($post)
{
	$full_name = get_post_meta($post->ID, '_full_name', true);
	$birth_date = get_post_meta($post->ID, '_birth_date', true);
	$gender = get_post_meta($post->ID, '_gender', true);
		?>
		<table class="form-table">
			<tr>
				<th><label>Full Name</label></th>
				<td><input type="text" value="<?php echo esc_attr($full_name); ?>" readonly style="width:100%;" /></td>
			</tr>
			<tr>
				<th><label>Date of Birth</label></th>
				<td><input type="date" value="<?php echo esc_attr($birth_date); ?>" readonly /></td>
			</tr>
			<tr>
				<th><label>Gender</label></th>
				<td><input type="text" value="<?php echo esc_attr($gender); ?>" readonly /></td>
			</tr>
		</table>
	<?php
}

function membership_contact_info_callback($post)
{
	$phone = get_post_meta($post->ID, '_phone_number', true);
	$email = get_post_meta($post->ID, '_email_address', true);
	$address = get_post_meta($post->ID, '_address', true);
	$country = get_post_meta($post->ID, '_country_residence', true);
	?>
		<table class="form-table">
			<tr>
				<th><label>Phone Number</label></th>
				<td><input type="text" value="<?php echo esc_attr($phone); ?>" readonly style="width:100%;" /></td>
			</tr>
			<tr>
				<th><label>Email Address</label></th>
				<td><input type="email" value="<?php echo esc_attr($email); ?>" readonly style="width:100%;" /></td>
			</tr>
			<tr>
				<th><label>Address</label></th>
				<td><textarea readonly style="width:100%;" rows="3"><?php echo esc_textarea($address); ?></textarea></td>
			</tr>
			<tr>
				<th><label>Country of Residence</label></th>
				<td><input type="text" value="<?php echo esc_attr($country); ?>" readonly style="width:100%;" /></td>
			</tr>
		</table>
	<?php
}

function membership_government_id_callback($post)
{
	$id_number = get_post_meta($post->ID, '_identification_number', true);
	$issuing_country = get_post_meta($post->ID, '_issuing_country', true);
	$expiration_date = get_post_meta($post->ID, '_expiration_date', true);
	$national_id = get_post_meta($post->ID, '_id_number', true);
	$passport_number = get_post_meta($post->ID, '_passport_number', true);
	$place_issue = get_post_meta($post->ID, '_place_issue', true);
	$date_issue = get_post_meta($post->ID, '_date_issue', true);
	?>
		<table class="form-table">
			<tr>
				<th><label>Identification Number</label></th>
				<td><input type="text" value="<?php echo esc_attr($id_number); ?>" readonly style="width:100%;" /></td>
			</tr>
			<tr>
				<th><label>Issuing Country</label></th>
				<td><input type="text" value="<?php echo esc_attr($issuing_country); ?>" readonly style="width:100%;" /></td>
			</tr>
			<tr>
				<th><label>Expiration Date</label></th>
				<td><input type="date" value="<?php echo esc_attr($expiration_date); ?>" readonly /></td>
			</tr>
			<tr>
				<th><label>National ID Number</label></th>
				<td><input type="text" value="<?php echo esc_attr($national_id); ?>" readonly style="width:100%;" /></td>
			</tr>
			<tr>
				<th><label>Passport Number</label></th>
				<td><input type="text" value="<?php echo esc_attr($passport_number); ?>" readonly style="width:100%;" /></td>
			</tr>
			<tr>
				<th><label>Place of Issue</label></th>
				<td><input type="text" value="<?php echo esc_attr($place_issue); ?>" readonly style="width:100%;" /></td>
			</tr>
			<tr>
				<th><label>Date of Issue</label></th>
				<td><input type="date" value="<?php echo esc_attr($date_issue); ?>" readonly /></td>
			</tr>
		</table>
	<?php
}

function membership_emergency_contact_callback($post)
{
	$contact_name = get_post_meta($post->ID, '_contact_name', true);
	$contact_relationship = get_post_meta($post->ID, '_contact_relationship', true);
	$contact_number = get_post_meta($post->ID, '_contact_number', true);
	$contact_email = get_post_meta($post->ID, '_contact_email', true);
	?>
		<table class="form-table">
			<tr>
				<th><label>Contact Name</label></th>
				<td><input type="text" value="<?php echo esc_attr($contact_name); ?>" readonly style="width:100%;" /></td>
			</tr>
			<tr>
				<th><label>Relationship</label></th>
				<td><input type="text" value="<?php echo esc_attr($contact_relationship); ?>" readonly style="width:100%;" /></td>
			</tr>
			<tr>
				<th><label>Phone Number</label></th>
				<td><input type="text" value="<?php echo esc_attr($contact_number); ?>" readonly style="width:100%;" /></td>
			</tr>
			<tr>
				<th><label>Email Address</label></th>
				<td><input type="email" value="<?php echo esc_attr($contact_email); ?>" readonly style="width:100%;" /></td>
			</tr>
		</table>
	<?php
}

function membership_professional_info_callback($post)
{
	$occupation = get_post_meta($post->ID, '_occupation', true);
	$employer = get_post_meta($post->ID, '_employer', true);
	$professional_aff = get_post_meta($post->ID, '_professional_aff', true);
	?>
		<table class="form-table">
			<tr>
				<th><label>Occupation</label></th>
				<td><input type="text" value="<?php echo esc_attr($occupation); ?>" readonly style="width:100%;" /></td>
			</tr>
			<tr>
				<th><label>Employer</label></th>
				<td><input type="text" value="<?php echo esc_attr($employer); ?>" readonly style="width:100%;" /></td>
			</tr>
			<tr>
				<th><label>Professional Affiliations</label></th>
				<td><input type="text" value="<?php echo esc_attr($professional_aff); ?>" readonly style="width:100%;" /></td>
			</tr>
		</table>
	<?php
}

function membership_documents_callback($post)
{
	$photo_url = get_post_meta($post->ID, '_photo_url', true);
	$signature_url = get_post_meta($post->ID, '_signature_url', true);
	?>
		<div style="margin-bottom: 15px;">
			<strong>Passport Photo:</strong><br>
			<?php if ($photo_url): ?>
				<a href="<?php echo esc_url($photo_url); ?>" target="_blank">
					<img src="<?php echo esc_url($photo_url); ?>" style="max-width: 150px; height: auto; border: 1px solid #ddd; padding: 5px;">
				</a>
				<br><a href="<?php echo esc_url($photo_url); ?>" target="_blank">View Full Size</a>
			<?php else: ?>
				<em>No photo uploaded</em>
			<?php endif; ?>
		</div>

		<div>
			<strong>Digital Signature:</strong><br>
			<?php if ($signature_url): ?>
				<a href="<?php echo esc_url($signature_url); ?>" target="_blank">
					<img src="<?php echo esc_url($signature_url); ?>" style="max-width: 200px; height: auto; border: 1px solid #ddd; padding: 5px;">
				</a>
				<br><a href="<?php echo esc_url($signature_url); ?>" target="_blank">View Full Size</a>
			<?php else: ?>
				<em>No signature uploaded</em>
			<?php endif; ?>
		</div>
	<?php
}

function membership_status_callback($post)
{
	wp_nonce_field('membership_status_nonce', 'membership_status_nonce');
	$status = get_post_meta($post->ID, '_application_status', true);
	$status = $status ? $status : 'pending';
	?>
		<select name="application_status" style="width: 100%;">
			<option value="pending" <?php selected($status, 'pending'); ?>>Pending</option>
			<option value="approved" <?php selected($status, 'approved'); ?>>Approved</option>
			<option value="rejected" <?php selected($status, 'rejected'); ?>>Rejected</option>
			<option value="under_review" <?php selected($status, 'under_review'); ?>>Under Review</option>
		</select>
		<p><em>Change the application status and update the post to save.</em></p>
		<?php
	}

	// Save the status meta box data
	add_action('save_post_membership_app', 'save_membership_status');
	function save_membership_status($post_id)
	{
		if (!isset($_POST['membership_status_nonce']) || !wp_verify_nonce($_POST['membership_status_nonce'], 'membership_status_nonce')) {
			return;
		}

		if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
			return;
		}

		if (!current_user_can('edit_post', $post_id)) {
			return;
		}

		if (isset($_POST['application_status'])) {
			update_post_meta($post_id, '_application_status', sanitize_text_field($_POST['application_status']));
		}
	}

	// Add some CSS for status styling
	add_action('admin_head', 'membership_admin_styles');
	function membership_admin_styles()
	{
		$screen = get_current_screen();
		if ($screen->post_type == 'membership_app') {
		?>
			<style>
				.status-pending {
					color: #f56500;
					font-weight: bold;
				}

				.status-approved {
					color: #46b450;
					font-weight: bold;
				}

				.status-rejected {
					color: #dc3232;
					font-weight: bold;
				}

				.status-under_review {
					color: #0073aa;
					font-weight: bold;
				}
			</style>
	<?php
		}
	}

	// QR Code generation function using endroid/qr-code
	function generate_qr_code($data, $filename = null)
	{
		try {
			if (!$filename) {
				$filename = 'qr_' . uniqid() . '.png';
			}

			$upload_dir = wp_upload_dir();
			$qr_dir = $upload_dir['basedir'] . '/qr-codes/';

			if (!file_exists($qr_dir)) {
				wp_mkdir_p($qr_dir);
			}

			$qr_file_path = $qr_dir . $filename;
			$qr_url = $upload_dir['baseurl'] . '/qr-codes/' . $filename;

			// Create QR code using endroid/qr-code

			$qrCode = new \Endroid\QrCode\QrCode(
				$data,
				null, // encoding (default UTF-8)
				null, // error correction level (default low)
				200,  // size
				10    // margin
			);

			$writer = new \Endroid\QrCode\Writer\PngWriter();
			$result = $writer->write($qrCode);

			// Save the QR code image
			file_put_contents($qr_file_path, $result->getString());

			return $qr_url;
		} catch (Exception $e) {
			error_log('QR Code generation failed: ' . $e->getMessage());
			return false;
		}
	}

	// Generate unique 15-character login key with timestamp
	function generate_login_key($attempt = 0)
	{
		// Prevent infinite recursion - max 10 attempts
		if ($attempt >= 10) {
			error_log('Failed to generate unique login key after 10 attempts');
			throw new Exception('Unable to generate unique login key. Please try again.');
		}

		// Get current timestamp in microseconds for maximum uniqueness
		$timestamp = microtime(true);
		// Add attempt number to timestamp for additional uniqueness
		$timestamp_with_attempt = $timestamp + ($attempt * 0.001);
		$timestamp_hash = substr(md5($timestamp_with_attempt), 0, 6); // 6 chars from timestamp

		// Generate 9-character random string (15 - 6 = 9)
		$characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
		$random_part = '';
		for ($i = 0; $i < 9; $i++) {
			$random_part .= $characters[random_int(0, strlen($characters) - 1)];
		}

		// Combine timestamp hash + random part = exactly 15 characters
		$login_key = strtoupper($timestamp_hash . $random_part);

		// Ensure uniqueness by checking database
		global $wpdb;
		$table_name = $wpdb->prefix . 'user_auth_keys';

		// Check if table exists first
		$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
		if (!$table_exists) {
			error_log('User auth keys table does not exist');
			throw new Exception('Database table not found. Please contact administrator.');
		}

		$existing = $wpdb->get_var($wpdb->prepare(
			"SELECT COUNT(*) FROM $table_name WHERE login_key = %s",
			$login_key
		));

		// If key exists, generate a new one recursively with incremented attempt counter
		if ($existing > 0) {
			error_log("Login key collision detected on attempt " . ($attempt + 1) . ": " . $login_key);
			return generate_login_key($attempt + 1);
		}

		// Log successful generation for monitoring
		if ($attempt > 0) {
			error_log("Unique login key generated after " . ($attempt + 1) . " attempts: " . $login_key);
		}

		return $login_key;
	}

	// Check if activation key already exists in database
	function is_activation_key_unique($login_key)
	{
		global $wpdb;
		$table_name = $wpdb->prefix . 'user_auth_keys';

		// Check if table exists first
		$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
		if (!$table_exists) {
			return false; // Table doesn't exist, can't verify uniqueness
		}

		$existing = $wpdb->get_var($wpdb->prepare(
			"SELECT COUNT(*) FROM $table_name WHERE login_key = %s",
			$login_key
		));

		return $existing == 0; // Return true if key is unique (count is 0)
	}

	// Check for duplicate entries
	function check_duplicate_entry($email, $phone)
	{
		global $wpdb;

		$existing_entry = $wpdb->get_row($wpdb->prepare("
			SELECT p.ID
			FROM {$wpdb->posts} p
			INNER JOIN {$wpdb->postmeta} pm1 ON p.ID = pm1.post_id
			INNER JOIN {$wpdb->postmeta} pm2 ON p.ID = pm2.post_id
			WHERE p.post_type = 'form_entries'
			AND p.post_status = 'publish'
			AND ((pm1.meta_key = '_email_address' AND pm1.meta_value = %s)
			OR (pm2.meta_key = '_phone_number' AND pm2.meta_value = %s))
		", $email, $phone));

		return $existing_entry ? true : false;
	}

	// Check for duplicate email only
	function check_duplicate_email($email)
	{
		global $wpdb;

		$existing_entry = $wpdb->get_row($wpdb->prepare("
			SELECT p.ID
			FROM {$wpdb->posts} p
			INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
			WHERE p.post_type = 'form_entries'
			AND p.post_status = 'publish'
			AND pm.meta_key = '_email_address'
			AND pm.meta_value = %s
		", $email));

		return $existing_entry ? true : false;
	}

	// Check for duplicate phone only
	function check_duplicate_phone($phone)
	{
		global $wpdb;

		$existing_entry = $wpdb->get_row($wpdb->prepare("
			SELECT p.ID
			FROM {$wpdb->posts} p
			INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
			WHERE p.post_type = 'form_entries'
			AND p.post_status = 'publish'
			AND pm.meta_key = '_phone_number'
			AND pm.meta_value = %s
		", $phone));

		return $existing_entry ? true : false;
	}

	// Handle membership form submission
	add_action('admin_post_handle_membership_form', 'handle_membership_form_submission');
	add_action('admin_post_nopriv_handle_membership_form', 'handle_membership_form_submission');

	// Handle resend login ID action
	add_action('admin_post_resend_login_id', 'handle_resend_login_id');

	function handle_resend_login_id()
	{
		// Check if user has permission
		if (!current_user_can('edit_posts')) {
			wp_die('You do not have permission to perform this action.');
		}

		// Verify nonce
		$post_id = intval($_GET['post_id']);
		if (!wp_verify_nonce($_GET['_wpnonce'], 'resend_login_' . $post_id)) {
			wp_die('Security check failed. Please try again.');
		}

		// Verify post exists and is a form entry
		$post = get_post($post_id);
		if (!$post || $post->post_type !== 'form_entries') {
			wp_die('Invalid form entry.');
		}

		// Get user data
		$email = get_post_meta($post_id, '_email_address', true);
		$full_name = get_post_meta($post_id, '_full_name', true);

		if (!$email) {
			wp_die('No email address found for this entry.');
		}

		// Get existing login key from database
		global $wpdb;
		$table_name = $wpdb->prefix . 'user_auth_keys';

		$auth_record = $wpdb->get_row($wpdb->prepare(
			"SELECT login_key FROM $table_name WHERE user_id = %d AND is_active = 1",
			$post_id
		));

		if (!$auth_record) {
			wp_die('No active login key found for this user.');
		}

		$login_key = $auth_record->login_key;

		// Generate QR code (reuse existing QR code or create new one)
		$login_url = home_url('/login?user_id=' . $post_id);
		$qr_filename = 'qr_user_' . $post_id . '.png';
		$qr_code_url = generate_qr_code($login_url, $qr_filename);

		// Prepare form data for email template
		$form_data = array(
			'email_address' => $email,
			'full_name' => $full_name
		);

		// Send the email using existing function
		send_user_confirmation_email($form_data, $login_key, $qr_code_url, $login_url);

		// Redirect back to admin with success message
		$redirect_url = add_query_arg(
			array(
				'post_type' => 'form_entries',
				'resend_success' => '1',
				'user_name' => urlencode($full_name)
			),
			admin_url('edit.php')
		);

		wp_redirect($redirect_url);
		exit;
	}

	// Display admin notice for resend success
	add_action('admin_notices', 'display_resend_login_notice');
	function display_resend_login_notice()
	{
		if (isset($_GET['resend_success']) && $_GET['resend_success'] == '1' && isset($_GET['user_name'])) {
			$user_name = urldecode($_GET['user_name']);
			echo '<div class="notice notice-success is-dismissible">';
			echo '<p><strong>Success!</strong> Login credentials have been resent to ' . esc_html($user_name) . '.</p>';
			echo '</div>';
		}
	}

	function handle_membership_form_submission()
	{
		// Verify nonce
		if (!wp_verify_nonce($_POST['membership_nonce'], 'membership_form_nonce')) {
			// Store error in session and redirect back
			session_start();
			$_SESSION['form_error'] = 'Security check failed. Please try again.';
			wp_redirect(wp_get_referer());
			exit;
		}

		// Start session for storing messages
		if (session_status() == PHP_SESSION_NONE) {
			session_start();
		}

		// Initialize validation errors array
		$validation_errors = array();

		// Collect and validate form data - using the actual form field names from join-now.php
		$form_data = array(
			'full_name' => sanitize_text_field($_POST['full-name']),
			'email_address' => sanitize_email($_POST['email-address']),
			'phone_number' => sanitize_text_field($_POST['phone-number']),
			'birth_date' => sanitize_text_field($_POST['birth-date']),
			'gender' => sanitize_text_field($_POST['gender']),
			'address' => sanitize_textarea_field($_POST['address']),
			'country_residence' => sanitize_text_field($_POST['country-residence']),
			'identification_number' => sanitize_text_field($_POST['identification-number']),
			'issuing_country' => sanitize_text_field($_POST['issuing-country']),
			'expiration_date' => sanitize_text_field($_POST['expiration-date']),
			'id_number' => sanitize_text_field($_POST['id-number']),
			'passport_number' => sanitize_text_field($_POST['passport-number']),
			'place_issue' => sanitize_text_field($_POST['place-issue']),
			'date_issue' => sanitize_text_field($_POST['date-issue']),
			'contact_name' => sanitize_text_field($_POST['contact-name']),
			'contact_relationship' => sanitize_text_field($_POST['contact-relationship']),
			'contact_number' => sanitize_text_field($_POST['contact-number']),
			'contact_email' => sanitize_email($_POST['contact-email']),
			'occupation' => sanitize_text_field($_POST['occupation']),
			'employer' => sanitize_text_field($_POST['employer']),
			'professional_aff' => sanitize_text_field($_POST['professional-aff'])
		);

		// Validate required fields
		if (empty($form_data['full_name'])) {
			$validation_errors['full-name'] = 'Full name is required.';
		}
		if (empty($form_data['email_address']) || !is_email($form_data['email_address'])) {
			$validation_errors['email-address'] = 'A valid email address is required.';
		}
		if (empty($form_data['phone_number'])) {
			$validation_errors['phone-number'] = 'Phone number is required.';
		}
		if (empty($form_data['birth_date'])) {
			$validation_errors['birth-date'] = 'Date of birth is required.';
		}
		if (empty($form_data['gender'])) {
			$validation_errors['gender'] = 'Gender is required.';
		}
		if (empty($form_data['address'])) {
			$validation_errors['address'] = 'Address is required.';
		}
		if (empty($form_data['country_residence'])) {
			$validation_errors['country-residence'] = 'Country of residence is required.';
		}

		// Validate file uploads
		if (empty($_FILES['photo-file']['name'])) {
			$validation_errors['photo-file'] = 'Passport photo is required.';
		} else {
			// Check photo file size and type
			if ($_FILES['photo-file']['size'] > 2 * 1024 * 1024) {
				$validation_errors['photo-file'] = 'Photo file size must be less than 2MB.';
			}
			$allowed_types = array('image/jpeg', 'image/jpg', 'image/png', 'image/gif');
			if (!in_array($_FILES['photo-file']['type'], $allowed_types)) {
				$validation_errors['photo-file'] = 'Photo must be a valid image file (JPEG, PNG, GIF).';
			}
		}

		if (empty($_FILES['signature-file']['name'])) {
			$validation_errors['signature-file'] = 'Digital signature is required.';
		} else {
			// Check signature file size and type
			if ($_FILES['signature-file']['size'] > 1 * 1024 * 1024) {
				$validation_errors['signature-file'] = 'Signature file size must be less than 1MB.';
			}
			$allowed_types = array('image/jpeg', 'image/jpg', 'image/png', 'image/gif');
			if (!in_array($_FILES['signature-file']['type'], $allowed_types)) {
				$validation_errors['signature-file'] = 'Signature must be a valid image file (JPEG, PNG, GIF).';
			}
		}

		// Check for duplicate email
		if (!empty($form_data['email_address']) && check_duplicate_email($form_data['email_address'])) {
			$validation_errors['email-address'] = 'An account with this email address already exists.';
		}

		// Check for duplicate phone
		if (!empty($form_data['phone_number']) && check_duplicate_phone($form_data['phone_number'])) {
			$validation_errors['phone-number'] = 'An account with this phone number already exists.';
		}

		// If there are validation errors, store them in session and redirect back
		if (!empty($validation_errors)) {
			$_SESSION['form_errors'] = $validation_errors;
			$_SESSION['form_data'] = $form_data;
			wp_redirect(wp_get_referer());
			exit;
		}

		// Handle file uploads with unique filenames
		$uploaded_files = array();

		if (!empty($_FILES['photo-file']['name'])) {
			$unique_filename = 'photo_' . uniqid() . '_' . sanitize_file_name($_FILES['photo-file']['name']);
			$photo_file = handle_file_upload('photo-file', 2 * 1024 * 1024, $unique_filename);
			if ($photo_file) {
				$uploaded_files['photo'] = $photo_file;
			} else {
				$_SESSION['form_error'] = 'Failed to upload photo. Please try again.';
				wp_redirect(wp_get_referer());
				exit;
			}
		}

		if (!empty($_FILES['signature-file']['name'])) {
			$unique_filename = 'signature_' . uniqid() . '_' . sanitize_file_name($_FILES['signature-file']['name']);
			$signature_file = handle_file_upload('signature-file', 1 * 1024 * 1024, $unique_filename);
			if ($signature_file) {
				$uploaded_files['signature'] = $signature_file;
			} else {
				$_SESSION['form_error'] = 'Failed to upload signature. Please try again.';
				wp_redirect(wp_get_referer());
				exit;
			}
		}

		// Create the post in form_entries
		$post_data = array(
			'post_title' => $form_data['full_name'],
			'post_type' => 'form_entries',
			'post_status' => 'publish',
			'meta_input' => array(
				'_full_name' => $form_data['full_name'],
				'_birth_date' => $form_data['birth_date'],
				'_gender' => $form_data['gender'],
				'_phone_number' => $form_data['phone_number'],
				'_email_address' => $form_data['email_address'],
				'_address' => $form_data['address'],
				'_country_residence' => $form_data['country_residence'],
				'_identification_number' => $form_data['identification_number'],
				'_issuing_country' => $form_data['issuing_country'],
				'_expiration_date' => $form_data['expiration_date'],
				'_id_number' => $form_data['id_number'],
				'_passport_number' => $form_data['passport_number'],
				'_place_issue' => $form_data['place_issue'],
				'_date_issue' => $form_data['date_issue'],
				'_contact_name' => $form_data['contact_name'],
				'_contact_relationship' => $form_data['contact_relationship'],
				'_contact_number' => $form_data['contact_number'],
				'_contact_email' => $form_data['contact_email'],
				'_occupation' => $form_data['occupation'],
				'_employer' => $form_data['employer'],
				'_professional_aff' => $form_data['professional_aff'],
				'_photo_url' => isset($uploaded_files['photo']) ? $uploaded_files['photo'] : '',
				'_signature_url' => isset($uploaded_files['signature']) ? $uploaded_files['signature'] : ''
			)
		);

		$post_id = wp_insert_post($post_data);

		if ($post_id) {
			// Generate login key and store in database
			$login_key = generate_login_key();
			// Set QR code to never expire - use a far future date (100 years from now)
			$expires_at = date('Y-m-d H:i:s', strtotime('+100 years'));

			global $wpdb;
			$table_name = $wpdb->prefix . 'user_auth_keys';

			$wpdb->insert(
				$table_name,
				array(
					'user_id' => $post_id,
					'login_key' => $login_key,
					'expires_at' => $expires_at,
					'is_active' => 1
				),
				array('%d', '%s', '%s', '%d')
			);

			// Generate QR code
			$login_url = home_url('/login?user_id=' . $post_id);
			$qr_filename = 'qr_user_' . $post_id . '.png';
			$qr_code_url = generate_qr_code($login_url, $qr_filename);

			// Send notification emails with QR code and login key
			send_form_entry_notification($form_data, $uploaded_files, $login_key, $qr_code_url, $login_url);

			// Clear any previous form data and errors from session
			unset($_SESSION['form_errors']);
			unset($_SESSION['form_data']);
			unset($_SESSION['form_error']);

			// Store success message in session
			$_SESSION['form_success'] = 'Your application has been submitted successfully! Please check your email for login instructions.';

			// Redirect back to the form page
			wp_redirect(wp_get_referer());
			exit;
		} else {
			$_SESSION['form_error'] = 'Failed to submit application. Please try again.';
			wp_redirect(wp_get_referer());
			exit;
		}
	}

	function handle_file_upload($file_key, $max_size, $custom_filename = null)
	{
		if (!isset($_FILES[$file_key])) {
			return false;
		}

		$file = $_FILES[$file_key];

		// Check file size
		if ($file['size'] > $max_size) {
			wp_die('File size too large');
		}

		// Check file type
		$allowed_types = array('image/jpeg', 'image/jpg', 'image/png', 'image/gif');
		if (!in_array($file['type'], $allowed_types)) {
			wp_die('Invalid file type');
		}

		// Upload file
		require_once(ABSPATH . 'wp-admin/includes/file.php');

		$upload_overrides = array('test_form' => false);

		// Use custom filename if provided
		if ($custom_filename) {
			$upload_overrides['unique_filename_callback'] = function ($dir, $name, $ext) use ($custom_filename) {
				return $custom_filename;
			};
		}

		$movefile = wp_handle_upload($file, $upload_overrides);

		if ($movefile && !isset($movefile['error'])) {
			return $movefile['url'];
		}

		return false;
	}

	// Send form entry notification with QR code and login key
	function send_form_entry_notification($form_data, $uploaded_files, $login_key, $qr_code_url, $login_url)
	{
		// Admin notification
		$admin_email = '<EMAIL>';
		$subject = 'New Form Entry Submitted';

		$message = "A new form entry has been submitted:\n\n";
		$message .= "Customer Name: " . $form_data['full_name'] . "\n";
		$message .= "Email: " . $form_data['email_address'] . "\n";
		$message .= "Phone: " . $form_data['phone_number'] . "\n";
		$message .= "Country: " . $form_data['country_residence'] . "\n";
		$message .= "Occupation: " . $form_data['occupation'] . "\n\n";
		$message .= "Login URL: " . $login_url . "\n";
		$message .= "Login Key: " . $login_key . "\n";

		wp_mail($admin_email, $subject, $message);

		// User confirmation email with HTML template
		send_user_confirmation_email($form_data, $login_key, $qr_code_url, $login_url);
	}

	function send_membership_notification($form_data, $uploaded_files)
	{
		// $admin_email = get_option('admin_email');
		$admin_email = '<EMAIL>';
		$subject = 'New Membership Application Submitted';

		$message = "A new membership application has been submitted:\n\n";
		$message .= "Personal Information:\n";
		$message .= "Full Name: " . $form_data['full_name'] . "\n";
		$message .= "Date of Birth: " . $form_data['birth_date'] . "\n";
		$message .= "Gender: " . $form_data['gender'] . "\n\n";

		$message .= "Contact Information:\n";
		$message .= "Phone: " . $form_data['phone_number'] . "\n";
		$message .= "Email: " . $form_data['email_address'] . "\n";
		$message .= "Address: " . $form_data['address'] . "\n";
		$message .= "Country: " . $form_data['country_residence'] . "\n\n";

		$message .= "Government Identification:\n";
		$message .= "ID Number: " . $form_data['identification_number'] . "\n";
		$message .= "Issuing Country: " . $form_data['issuing_country'] . "\n";
		$message .= "Expiration Date: " . $form_data['expiration_date'] . "\n";
		$message .= "National ID: " . $form_data['id_number'] . "\n";
		$message .= "Passport Number: " . $form_data['passport_number'] . "\n";
		$message .= "Place of Issue: " . $form_data['place_issue'] . "\n";
		$message .= "Date of Issue: " . $form_data['date_issue'] . "\n\n";

		$message .= "Emergency Contact:\n";
		$message .= "Name: " . $form_data['contact_name'] . "\n";
		$message .= "Relationship: " . $form_data['contact_relationship'] . "\n";
		$message .= "Phone: " . $form_data['contact_number'] . "\n";
		$message .= "Email: " . $form_data['contact_email'] . "\n\n";

		$message .= "Professional Information:\n";
		$message .= "Occupation: " . $form_data['occupation'] . "\n";
		$message .= "Employer: " . $form_data['employer'] . "\n";
		$message .= "Professional Affiliations: " . $form_data['professional_aff'] . "\n\n";

		if (!empty($uploaded_files['photo'])) {
			$message .= "Photo: " . $uploaded_files['photo'] . "\n";
		}
		if (!empty($uploaded_files['signature'])) {
			$message .= "Signature: " . $uploaded_files['signature'] . "\n";
		}

		wp_mail($admin_email, $subject, $message);

		// Send confirmation email to applicant
		$applicant_subject = 'Membership Application Received';
		$applicant_message = "Dear " . $form_data['full_name'] . ",\n\n";
		$applicant_message .= "Thank you for submitting your membership application. We have received your information and will review it shortly.\n\n";
		$applicant_message .= "Best regards,\nMembership Team";

		wp_mail($form_data['email_address'], $applicant_subject, $applicant_message);
	}

	// Send user confirmation email with HTML template
	function send_user_confirmation_email($form_data, $login_key, $qr_code_url, $login_url)
	{
		$to = $form_data['email_address'];
		$subject = 'Submission Confirmation - Access Credentials';

		// Get the HTML template
		$html_template = get_email_template();

		// Replace placeholders
		$html_content = str_replace('[LOGIN_KEY_PLACEHOLDER]', $login_key, $html_template);
		$html_content = str_replace('[QR_URL_PLACEHOLDER]', $login_url, $html_content);

		// If QR code was generated successfully, embed it
		if ($qr_code_url) {
			$qr_img_tag = '<img src="' . esc_url($qr_code_url) . '" alt="QR Code" style="width: 200px; height: 200px;">';
			$html_content = str_replace('QR Code Will Appear Here', $qr_img_tag, $html_content);
		}

		// Set headers for HTML email
		$headers = array(
			'Content-Type: text/html; charset=UTF-8',
			'From: ' . get_bloginfo('name') . ' <' . get_option('admin_email') . '>'
		);

		wp_mail($to, $subject, $html_content, $headers);
	}

	// Display success/error messages and handle form validation
	add_action('wp_head', 'display_membership_messages');
	function display_membership_messages()
	{
		if (isset($_GET['membership_status'])) {
			if ($_GET['membership_status'] == 'success') {
				echo '<script>
	            document.addEventListener("DOMContentLoaded", function() {
	                // Show success message below form
	                var form = document.querySelector("form[action*=\"handle_membership_form\"]");
	                if (form) {
	                    var successDiv = document.createElement("div");
	                    successDiv.className = "form-success-message";
	                    successDiv.style.cssText = "background: #d4edda; color: #155724; padding: 15px; border: 1px solid #c3e6cb; border-radius: 4px; margin: 20px 0;";
	                    successDiv.innerHTML = "✅ Thank you! Your form has been submitted successfully. Please check your email for access credentials.";
	                    form.parentNode.insertBefore(successDiv, form.nextSibling);

	                    // Reset form
	                    form.reset();

	                    // Scroll to success message
	                    successDiv.scrollIntoView({ behavior: "smooth", block: "center" });
	                }
	            });
	        </script>';
			} elseif ($_GET['membership_status'] == 'validation_error') {
				$errors = isset($_GET['errors']) ? json_decode(base64_decode($_GET['errors']), true) : array();
				$form_data = isset($_GET['form_data']) ? json_decode(base64_decode($_GET['form_data']), true) : array();

				echo '<script>
	            document.addEventListener("DOMContentLoaded", function() {
	                var errors = ' . json_encode($errors) . ';
	                var formData = ' . json_encode($form_data) . ';
	                var firstErrorField = null;

	                // Display validation errors
	                for (var fieldName in errors) {
	                    var field = document.querySelector("[name=\"" + fieldName.replace("_", "-") + "\"]");
	                    if (field) {
	                        // Remove existing error messages
	                        var existingError = field.parentNode.querySelector(".field-error");
	                        if (existingError) {
	                            existingError.remove();
	                        }

	                        // Add error message
	                        var errorDiv = document.createElement("div");
	                        errorDiv.className = "field-error";
	                        errorDiv.style.cssText = "color: #dc3545; font-size: 14px; margin-top: 5px; font-weight: 500;";
	                        errorDiv.textContent = errors[fieldName];
	                        field.parentNode.appendChild(errorDiv);

	                        // Add error styling to field
	                        field.style.borderColor = "#dc3545";
	                        field.style.boxShadow = "0 0 0 0.2rem rgba(220, 53, 69, 0.25)";

	                        // Track first error field
	                        if (!firstErrorField) {
	                            firstErrorField = field;
	                        }
	                    }
	                }

	                // Restore form data
	                for (var fieldName in formData) {
	                    var field = document.querySelector("[name=\"" + fieldName.replace("_", "-") + "\"]");
	                    if (field && formData[fieldName]) {
	                        if (field.type === "checkbox") {
	                            field.checked = formData[fieldName] === "yes";
	                        } else {
	                            field.value = formData[fieldName];
	                        }
	                    }
	                }

	                // Scroll to first error field
	                if (firstErrorField) {
	                    firstErrorField.scrollIntoView({ behavior: "smooth", block: "center" });
	                    firstErrorField.focus();
	                }
	            });
	        </script>';
			}
		}
	}

	// Get HTML email template
	function get_email_template()
	{
		return '<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Submission Confirmation</title>
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body { font-family: \'Segoe UI\', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; background-color: #f4f4f4; }
    .container { max-width: 600px; margin: 20px auto; background: #ffffff; border-radius: 10px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1); overflow: hidden; }
    .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
    .header h1 { font-size: 24px; margin-bottom: 10px; }
    .checkmark { width: 60px; height: 60px; border-radius: 50%; background: rgba(255, 255, 255, 0.2); margin: 0 auto 20px; display: flex; align-items: center; justify-content: center; font-size: 30px; }
    .content { padding: 40px 30px; }
    .message { font-size: 16px; margin-bottom: 30px; color: #555; }
    .credentials-section { background: #f8f9fa; border-left: 4px solid #667eea; padding: 25px; margin: 25px 0; border-radius: 5px; }
    .credential-item { margin-bottom: 20px; }
    .credential-label { font-weight: bold; color: #333; font-size: 14px; text-transform: uppercase; letter-spacing: 0.5px; margin-bottom: 8px; }
    .credential-value { background: #fff; padding: 12px 15px; border: 2px solid #e9ecef; border-radius: 5px; font-family: \'Courier New\', monospace; font-size: 16px; font-weight: bold; color: #2c3e50; word-break: break-all; }
    .qr-section { text-align: center; margin: 30px 0; padding: 25px; background: #f8f9fa; border-radius: 8px; }
    .qr-placeholder { width: 200px; height: 200px; background: #fff; border: 2px dashed #ccc; margin: 0 auto 15px; display: flex; align-items: center; justify-content: center; color: #666; font-size: 14px; border-radius: 8px; }
    .access-button { display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 12px 30px; text-decoration: none; border-radius: 25px; font-weight: bold; margin-top: 15px; }
    .security-warning { background: #fff3cd; border-left: 4px solid #ffc107; padding: 20px; margin: 25px 0; border-radius: 5px; }
    .warning-icon { color: #856404; font-size: 20px; margin-right: 10px; }
    .security-text { color: #856404; font-weight: 500; }
    .footer { background: #f8f9fa; padding: 25px; text-align: center; color: #666; font-size: 14px; border-top: 1px solid #e9ecef; }
    .divider { height: 2px; background: linear-gradient(to right, transparent, #667eea, transparent); margin: 30px 0; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="checkmark">✓</div>
      <h1>Submission Successful!</h1>
      <p>Your submission has been received and processed</p>
    </div>
    <div class="content">
      <p class="message">Thank you for your submission. Below are your login credentials and QR code to access your submissions. Please keep this information secure.</p>
      <div class="divider"></div>
      <div class="credentials-section">
        <h3 style="margin-bottom: 20px; color: #333;">Access Credentials</h3>
        <div class="credential-item">
          <div class="credential-label">Login Key</div>
          <div class="credential-value">[LOGIN_KEY_PLACEHOLDER]</div>
        </div>
      </div>
      <div class="qr-section">
        <h3 style="margin-bottom: 15px; color: #333;">Quick Access</h3>
        <div class="qr-placeholder">QR Code Will Appear Here</div>
        <p style="color: #666; margin-bottom: 10px;">Scan the QR code above or click the button below</p>
        <a href="[QR_URL_PLACEHOLDER]" class="access-button">Access Your Submissions</a>
      </div>
      <div class="security-warning">
        <div style="display: flex; align-items: flex-start;">
          <span class="warning-icon">⚠️</span>
          <div>
            <strong class="security-text">Important Security Notice</strong>
            <p class="security-text" style="margin-top: 5px;">Do not share your login key with anyone. Keep this information confidential and secure. This key provides access to your personal submissions and data.</p>
          </div>
        </div>
      </div>
    </div>
    <div class="footer">
      <p>If you have any questions or need assistance, please contact our support team.</p>
      <p style="margin-top: 10px; font-size: 12px; color: #999;">This is an automated message. Please do not reply to this email.</p>
    </div>
  </div>
</body>
</html>';
	}

	// AJAX handler for session refresh
	add_action('wp_ajax_refresh_session', 'refresh_user_session');
	add_action('wp_ajax_nopriv_refresh_session', 'refresh_user_session');

	function refresh_user_session()
	{
		session_start();
		if (isset($_SESSION['authenticated_user_id'])) {
			$_SESSION['login_time'] = time();
			wp_send_json_success('Session refreshed');
		} else {
			wp_send_json_error('No active session');
		}
	}

	// Add custom admin columns for form_entries
	add_filter('manage_form_entries_posts_columns', 'set_custom_form_entries_columns');
	function set_custom_form_entries_columns($columns)
	{
		unset($columns['date']);
		$columns['customer_name'] = __('Customer Name', 'textdomain');
		$columns['email'] = __('Email', 'textdomain');
		$columns['phone'] = __('Phone', 'textdomain');
		$columns['country'] = __('Country', 'textdomain');
		$columns['date'] = __('Date', 'textdomain');
		return $columns;
	}

	// Add custom admin column data for form_entries
	add_action('manage_form_entries_posts_custom_column', 'custom_form_entries_column', 10, 2);
	function custom_form_entries_column($column, $post_id)
	{
		switch ($column) {
			case 'customer_name':
				echo get_post_meta($post_id, '_full_name', true);
				break;
			case 'email':
				echo get_post_meta($post_id, '_email_address', true);
				break;
			case 'phone':
				echo get_post_meta($post_id, '_phone_number', true);
				break;
			case 'country':
				echo get_post_meta($post_id, '_country_residence', true);
				break;
		}
	}

	// Make custom columns sortable
	add_filter('manage_edit-form_entries_sortable_columns', 'form_entries_sortable_columns');
	function form_entries_sortable_columns($columns)
	{
		$columns['customer_name'] = 'customer_name';
		$columns['email'] = 'email';
		$columns['phone'] = 'phone';
		$columns['country'] = 'country';
		return $columns;
	}

	// Add meta boxes for form_entries editing
	add_action('add_meta_boxes', 'add_form_entries_meta_boxes');
	function add_form_entries_meta_boxes()
	{
		add_meta_box(
			'form_entries_details',
			'Form Entry Details',
			'form_entries_meta_box_callback',
			'form_entries',
			'normal',
			'high'
		);
	}

	// Meta box callback function
	function form_entries_meta_box_callback($post)
	{
		// Add nonce for security
		wp_nonce_field('form_entries_meta_box', 'form_entries_meta_box_nonce');

		// Get current values
		$fields = array(
			'_full_name' => 'Full Name',
			'_birth_date' => 'Date of Birth',
			'_gender' => 'Gender',
			'_phone_number' => 'Phone Number',
			'_email_address' => 'Email Address',
			'_address' => 'Address',
			'_country_residence' => 'Country of Residence',
			'_identification_number' => 'Identification Number',
			'_issuing_country' => 'Issuing Country',
			'_expiration_date' => 'Expiration Date',
			'_id_number' => 'National ID Number',
			'_passport_number' => 'Passport Number',
			'_place_issue' => 'Place of Issue',
			'_date_issue' => 'Date of Issue',
			'_contact_name' => 'Emergency Contact Name',
			'_contact_relationship' => 'Contact Relationship',
			'_contact_number' => 'Contact Phone Number',
			'_contact_email' => 'Contact Email',
			'_occupation' => 'Occupation',
			'_employer' => 'Employer',
			'_professional_aff' => 'Professional Affiliations'
		);

		echo '<table class="form-table">';
		foreach ($fields as $field_key => $field_label) {
			$value = get_post_meta($post->ID, $field_key, true);
			echo '<tr>';
			echo '<th scope="row"><label for="' . $field_key . '">' . $field_label . '</label></th>';
			echo '<td><input type="text" id="' . $field_key . '" name="' . $field_key . '" value="' . esc_attr($value) . '" class="regular-text" /></td>';
			echo '</tr>';
		}

		// Display uploaded files (read-only)
		$photo_url = get_post_meta($post->ID, '_photo_url', true);
		$signature_url = get_post_meta($post->ID, '_signature_url', true);

		echo '<tr>';
		echo '<th scope="row">Passport Photo</th>';
		echo '<td>';
		if ($photo_url) {
			echo '<img src="' . esc_url($photo_url) . '" style="max-width: 200px; max-height: 200px;" /><br>';
			echo '<small>File cannot be edited for security reasons</small>';
		} else {
			echo '<em>No photo uploaded</em>';
		}
		echo '</td>';
		echo '</tr>';

		echo '<tr>';
		echo '<th scope="row">Digital Signature</th>';
		echo '<td>';
		if ($signature_url) {
			echo '<img src="' . esc_url($signature_url) . '" style="max-width: 200px; max-height: 200px;" /><br>';
			echo '<small>Signature cannot be edited for security reasons</small>';
		} else {
			echo '<em>No signature uploaded</em>';
		}
		echo '</td>';
		echo '</tr>';

		echo '</table>';
	}

	// Save meta box data
	add_action('save_post', 'save_form_entries_meta_box_data');
	function save_form_entries_meta_box_data($post_id)
	{
		// Check if nonce is valid
		if (
			!isset($_POST['form_entries_meta_box_nonce']) ||
			!wp_verify_nonce($_POST['form_entries_meta_box_nonce'], 'form_entries_meta_box')
		) {
			return;
		}

		// Check if user has permission to edit
		if (!current_user_can('edit_post', $post_id)) {
			return;
		}

		// Check if this is an autosave
		if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
			return;
		}

		// Check post type
		if (get_post_type($post_id) !== 'form_entries') {
			return;
		}

		// Get original data for comparison
		$original_data = array();
		$fields = array(
			'_full_name',
			'_birth_date',
			'_gender',
			'_phone_number',
			'_email_address',
			'_address',
			'_country_residence',
			'_identification_number',
			'_issuing_country',
			'_expiration_date',
			'_id_number',
			'_passport_number',
			'_place_issue',
			'_date_issue',
			'_contact_name',
			'_contact_relationship',
			'_contact_number',
			'_contact_email',
			'_occupation',
			'_employer',
			'_professional_aff'
		);

		foreach ($fields as $field) {
			$original_data[$field] = get_post_meta($post_id, $field, true);
		}

		// Save the data
		$changes_made = false;
		foreach ($fields as $field) {
			if (isset($_POST[$field])) {
				$new_value = sanitize_text_field($_POST[$field]);
				$old_value = get_post_meta($post_id, $field, true);

				if ($new_value !== $old_value) {
					update_post_meta($post_id, $field, $new_value);
					$changes_made = true;
				}
			}
		}

		// Send notification email if changes were made
		if ($changes_made) {
			send_admin_edit_notification($post_id, $original_data);
		}
	}

	// Send notification when admin edits data
	function send_admin_edit_notification($post_id, $original_data)
	{
		$email = get_post_meta($post_id, '_email_address', true);
		$name = get_post_meta($post_id, '_full_name', true);

		if (!$email) return;

		$subject = 'Your Form Data Has Been Updated';
		$message = "Dear " . $name . ",\n\n";
		$message .= "This is to inform you that your form submission data has been updated by our administrative team.\n\n";
		$message .= "If you have any questions about these changes, please contact our support team.\n\n";
		$message .= "Best regards,\nAdministrative Team";

		$headers = array('Content-Type: text/plain; charset=UTF-8');
		wp_mail($email, $subject, $message, $headers);
	}

	// Create necessary pages on theme activation
	add_action('after_switch_theme', 'create_form_system_pages');
	function create_form_system_pages()
	{
		// Create login page
		$login_page = get_page_by_path('login');
		if (!$login_page) {
			$login_page_id = wp_insert_post(array(
				'post_title' => 'Login',
				'post_name' => 'login',
				'post_content' => 'This page uses the User Login template.',
				'post_status' => 'publish',
				'post_type' => 'page'
			));

			// Set the page template
			if ($login_page_id && !is_wp_error($login_page_id)) {
				update_post_meta($login_page_id, '_wp_page_template', 'page-login.php');
			}
		}

		// Create view submissions page
		$submissions_page = get_page_by_path('view-submissions');
		if (!$submissions_page) {
			$submissions_page_id = wp_insert_post(array(
				'post_title' => 'View Submissions',
				'post_name' => 'view-submissions',
				'post_content' => 'This page uses the View Submissions template.',
				'post_status' => 'publish',
				'post_type' => 'page'
			));

			// Set the page template
			if ($submissions_page_id && !is_wp_error($submissions_page_id)) {
				update_post_meta($submissions_page_id, '_wp_page_template', 'page-view-submissions.php');
			}
		}
	}

	// Also create pages on init if they don't exist (fallback)
	add_action('init', 'ensure_form_system_pages_exist');
	function ensure_form_system_pages_exist()
	{
		// Only run this once per day to avoid performance issues
		$last_check = get_option('form_pages_last_check', 0);
		if (time() - $last_check < DAY_IN_SECONDS) {
			return;
		}

		update_option('form_pages_last_check', time());

		// Check and create login page if missing
		$login_page = get_page_by_path('login');
		if (!$login_page) {
			$login_page_id = wp_insert_post(array(
				'post_title' => 'Login',
				'post_name' => 'login',
				'post_content' => 'This page uses the User Login template.',
				'post_status' => 'publish',
				'post_type' => 'page'
			));

			if ($login_page_id && !is_wp_error($login_page_id)) {
				update_post_meta($login_page_id, '_wp_page_template', 'page-login.php');
			}
		}

		// Check and create view submissions page if missing
		$submissions_page = get_page_by_path('view-submissions');
		if (!$submissions_page) {
			$submissions_page_id = wp_insert_post(array(
				'post_title' => 'View Submissions',
				'post_name' => 'view-submissions',
				'post_content' => 'This page uses the View Submissions template.',
				'post_status' => 'publish',
				'post_type' => 'page'
			));

			if ($submissions_page_id && !is_wp_error($submissions_page_id)) {
				update_post_meta($submissions_page_id, '_wp_page_template', 'page-view-submissions.php');
			}
		}

		// Function to manually create pages (can be called from admin or debugging)
		function create_pages_manually()
		{
			$results = array();

			// Create login page
			$login_page = get_page_by_path('login');
			if (!$login_page) {
				$login_page_id = wp_insert_post(array(
					'post_title' => 'Login',
					'post_name' => 'login',
					'post_content' => 'This page uses the User Login template.',
					'post_status' => 'publish',
					'post_type' => 'page'
				));

				if ($login_page_id && !is_wp_error($login_page_id)) {
					update_post_meta($login_page_id, '_wp_page_template', 'page-login.php');
					$results['login'] = 'Created login page with ID: ' . $login_page_id;
				} else {
					$results['login'] = 'Failed to create login page';
				}
			} else {
				$results['login'] = 'Login page already exists with ID: ' . $login_page->ID;
				// Ensure template is set
				update_post_meta($login_page->ID, '_wp_page_template', 'page-login.php');
			}

			// Create view submissions page
			$submissions_page = get_page_by_path('view-submissions');
			if (!$submissions_page) {
				$submissions_page_id = wp_insert_post(array(
					'post_title' => 'View Submissions',
					'post_name' => 'view-submissions',
					'post_content' => 'This page uses the View Submissions template.',
					'post_status' => 'publish',
					'post_type' => 'page'
				));

				if ($submissions_page_id && !is_wp_error($submissions_page_id)) {
					update_post_meta($submissions_page_id, '_wp_page_template', 'page-view-submissions.php');
					$results['submissions'] = 'Created view-submissions page with ID: ' . $submissions_page_id;
				} else {
					$results['submissions'] = 'Failed to create view-submissions page';
				}
			} else {
				$results['submissions'] = 'View-submissions page already exists with ID: ' . $submissions_page->ID;
				// Ensure template is set
				update_post_meta($submissions_page->ID, '_wp_page_template', 'page-view-submissions.php');
			}

			return $results;
		}

		// Force create pages now (for immediate setup)
		add_action('wp_loaded', 'force_create_pages_once');
		function force_create_pages_once()
		{
			// Only run once
			if (get_option('form_pages_created', false)) {
				return;
			}

			create_pages_manually();
			update_option('form_pages_created', true);
		}
	}
